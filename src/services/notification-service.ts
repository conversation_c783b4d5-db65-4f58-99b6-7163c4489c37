import bot from "../bot";
import { T } from "../i18n";
import { botMessages } from "../intl/messages";
import { APP_NAME, PREM_RELAYER_USERNAME } from "../app.constants";
import { log } from "../utils/logger";
import { getFirestore } from "../firebase/firebase-admin";

export interface NotifyBuyParams {
  orderId: string;
  sellerId: string;
  orderNumber?: number;
  price?: number;
}

export interface NotifyGiftSendParams {
  orderId: string;
  buyerId: string;
  orderNumber?: number;
}

export interface UserData {
  tg_id: string;
  userLanguage?: string;
}

// Get bot username from bot instance
let cachedBotUsername: string | null = null;

async function getBotUsername() {
  if (cachedBotUsername) {
    return cachedBotUsername;
  }

  try {
    const botInfo = await bot.telegram.getMe();
    cachedBotUsername = botInfo.username || `${APP_NAME.toLowerCase()}bot`;
    return cachedBotUsername;
  } catch (error) {
    log.error("Failed to get bot username", error);
    return `${APP_NAME.toLowerCase()}bot`; // fallback
  }
}

function createOrderDeepLink(botUsername: string, orderId: string): string {
  return `https://t.me/${botUsername}?startapp=order_${orderId}`;
}

export async function notifySellerOrderPaid(
  params: NotifyBuyParams
): Promise<{ success: boolean; message: string }> {
  try {
    const { orderId, sellerId, orderNumber, price } = params;

    log.info("Notifying seller of order payment", {
      operation: "notify_seller_order_paid",
      orderId,
      sellerId,
      orderNumber,
      price,
    });

    // Get user data from Firebase
    const db = getFirestore();
    const userDoc = await db.collection("users").doc(sellerId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${sellerId} not found`);
    }

    const userData = userDoc.data() as UserData;

    if (!userData.tg_id) {
      throw new Error(`User ${sellerId} does not have a Telegram ID`);
    }

    const botUsername = await getBotUsername();
    const orderLink = createOrderDeepLink(botUsername, orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.userLanguage || "en",
    };

    // Prepare message parameters
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      price: price ? price.toString() : "N/A",
      orderLink,
    };

    const message = T(
      ctx,
      botMessages.sellerOrderPaidWithLink.id,
      messageParams
    );

    // Send notification to seller
    await bot.telegram.sendMessage(userData.tg_id, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    log.info("Seller notification sent successfully", {
      operation: "notify_seller_order_paid",
      orderId,
      sellerId,
      tgId: userData.tg_id,
    });

    return {
      success: true,
      message: "Seller notification sent successfully",
    };
  } catch (error) {
    log.error("Failed to notify seller of order payment", error, {
      operation: "notify_seller_order_paid",
      orderId: params.orderId,
      sellerId: params.sellerId,
    });

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function notifyBuyerGiftSent(
  params: NotifyGiftSendParams
): Promise<{ success: boolean; message: string }> {
  try {
    const { orderId, buyerId, orderNumber } = params;

    log.info("Notifying buyer of gift sent", {
      operation: "notify_buyer_gift_sent",
      orderId,
      buyerId,
      orderNumber,
    });

    // Get user data from Firebase
    const db = getFirestore();
    const userDoc = await db.collection("users").doc(buyerId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${buyerId} not found`);
    }

    const userData = userDoc.data() as UserData;

    if (!userData.tg_id) {
      throw new Error(`User ${buyerId} does not have a Telegram ID`);
    }

    const botUsername = await getBotUsername();
    const orderLink = createOrderDeepLink(botUsername, orderId);

    // Create context for translation
    const ctx = {
      userLanguage: userData.userLanguage || "en",
    };

    // Prepare message parameters
    const messageParams = {
      orderNumber: orderNumber || "N/A",
      relayerUsername: PREM_RELAYER_USERNAME.replace("@", ""),
      orderLink,
    };

    const message = T(ctx, botMessages.buyerGiftSentWithLink.id, messageParams);

    // Send notification to buyer
    await bot.telegram.sendMessage(userData.tg_id, message, {
      parse_mode: "Markdown",
      link_preview_options: { is_disabled: true },
    });

    log.info("Buyer notification sent successfully", {
      operation: "notify_buyer_gift_sent",
      orderId,
      buyerId,
      tgId: userData.tg_id,
    });

    return {
      success: true,
      message: "Buyer notification sent successfully",
    };
  } catch (error) {
    log.error("Failed to notify buyer of gift sent", error, {
      operation: "notify_buyer_gift_sent",
      orderId: params.orderId,
      buyerId: params.buyerId,
    });

    return {
      success: false,
      message: `Failed to send notification: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}
