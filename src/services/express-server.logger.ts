import { log } from "../utils/logger";
import { LogOperations } from "../constants/bot-commands";

export const ExpressServerLogger = {
  logServerReadiness(params: { ready: boolean }) {
    log.info(`Server readiness: ${params.ready ? "READY" : "NOT READY"}`, {
      operation: LogOperations.HTTP_SERVER,
      ready: params.ready,
    });
  },

  logHttpRequest(params: {
    method: string | undefined;
    url: string | undefined;
  }) {
    log.info(`${params.method} ${params.url}`, {
      operation: LogOperations.HTTP_REQUEST,
      method: params.method,
      url: params.url,
    });
  },

  logHealthcheckError(params: { error: unknown }) {
    log.error("Error in healthcheck endpoint", params.error, {
      operation: LogOperations.HEALTHCHECK,
    });
  },

  logWebhookProcessingError(params: { error: unknown }) {
    log.error("Error processing webhook update ", params.error, {
      operation: LogOperations.WEBHOOK_PROCESSING,
    });
  },

  logRequestHandlingError(params: {
    error: Error;
    url: string;
    method: string;
  }) {
    log.error("Request handling error ", params.error, {
      operation: "http_request",
      url: params.url,
      method: params.method,
    });
  },

  logServerStarted(params: { port: number }) {
    log.info(`Express HTTP server running on port ${params.port}`, {
      operation: "http_server_start",
      port: params.port,
      healthcheckUrl: `http://localhost:${params.port}/healthcheck`,
    });
  },

  logServerError(params: { error: unknown; port: number }) {
    log.error("HTTP server error ", params.error, {
      operation: "http_server_start",
      port: params.port,
    });
  },

  logServerStopping() {
    log.info("Stopping Express HTTP server", {
      operation: "http_server_stop",
    });
  },

  logServerStopped() {
    log.info("Express HTTP server stopped", {
      operation: "http_server_stop",
      status: "completed",
    });
  },

  logWebhookReceived(params: { updateId?: number }) {
    log.webhookLog("Received webhook update - ", {
      operation: "webhook_processing",
      updateId: params.updateId,
    });
  },
};
