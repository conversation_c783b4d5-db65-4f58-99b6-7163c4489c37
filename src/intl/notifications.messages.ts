import { defineMessages } from "@formatjs/intl";

export const notificationsMessages = defineMessages({
  sellerOrderPaid: {
    id: "notifications.sellerOrderPaid",
    defaultMessage: "🎉 Congratulations! Your order #{orderNumber} has been purchased for {price} TON. You can now send the gift to complete the order.",
  },
  sellerOrderPaidWithLink: {
    id: "notifications.sellerOrderPaidWithLink",
    defaultMessage: "🎉 Congratulations! Your order #{orderNumber} has been purchased for {price} TON. You can now send the gift to complete the order.\n\n📱 View order details: {orderLink}",
  },
  buyerGiftSent: {
    id: "notifications.buyerGiftSent",
    defaultMessage: "🎁 Great news! The gift for your order #{orderNumber} has been sent to the bot. You can now claim it from @{relayerUsername}.",
  },
  buyerGiftSentWithLink: {
    id: "notifications.buyerGiftSentWithLink",
    defaultMessage: "🎁 Great news! The gift for your order #{orderNumber} has been sent to the bot. You can now claim it from @{relayerUsername}.\n\n📱 View order details: {orderLink}",
  },
});
